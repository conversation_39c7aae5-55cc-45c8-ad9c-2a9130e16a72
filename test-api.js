const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:5000/api';

async function testAPI() {
  console.log('🧪 Testing Backend API...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    const healthResponse = await fetch(`${API_BASE_URL.replace('/api', '')}/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check response:', healthData);
    console.log('');

    // Test 2: Sign in with valid credentials
    console.log('2. Testing sign in with valid credentials...');
    const signInResponse = await fetch(`${API_BASE_URL}/auth/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Agent123!'
      }),
    });

    console.log('Response status:', signInResponse.status);
    console.log('Response headers:', Object.fromEntries(signInResponse.headers.entries()));

    const signInData = await signInResponse.json();
    console.log('✅ Sign in response:', JSON.stringify(signInData, null, 2));
    console.log('');

    // Test 3: Sign in with invalid credentials
    console.log('3. Testing sign in with invalid credentials...');
    const invalidResponse = await fetch(`${API_BASE_URL}/auth/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      }),
    });

    const invalidData = await invalidResponse.json();
    console.log('✅ Invalid sign in response:', JSON.stringify(invalidData, null, 2));
    console.log('');

    // Test 4: Test with missing fields
    console.log('4. Testing sign in with missing fields...');
    const missingFieldsResponse = await fetch(`${API_BASE_URL}/auth/signin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
        // missing password
      }),
    });

    const missingFieldsData = await missingFieldsResponse.json();
    console.log('✅ Missing fields response:', JSON.stringify(missingFieldsData, null, 2));
    console.log('');

    console.log('🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testAPI(); 