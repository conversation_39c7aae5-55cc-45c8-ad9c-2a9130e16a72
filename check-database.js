const mongoose = require('mongoose');
const Agent = require('./models/Agent');
const bcrypt = require('bcryptjs');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://usman:<EMAIL>/LeadGenApp';

async function checkDatabase() {
  try {
    console.log('🔍 Checking database contents...\n');

    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ Connected to MongoDB\n');

    // Get all agents
    const agents = await Agent.find({});
    console.log(`📊 Found ${agents.length} agents in database:\n`);

    if (agents.length === 0) {
      console.log('❌ No agents found in database!');
      console.log('💡 Run "npm run seed" to populate the database');
      return;
    }

    // Display agent information
    agents.forEach((agent, index) => {
      console.log(`${index + 1}. ${agent.fullName} (${agent.email})`);
      console.log(`   Role: ${agent.role}`);
      console.log(`   Active: ${agent.isActive}`);
      console.log(`   Login Attempts: ${agent.loginAttempts}`);
      console.log(`   Locked: ${agent.isLocked()}`);
      console.log(`   Created: ${agent.createdAt.toLocaleDateString()}`);
      console.log('');
    });

    // Test specific credentials
    console.log('🧪 Testing specific credentials...\n');

    const testCredentials = [
      { email: '<EMAIL>', password: 'Admin123!' },
      { email: '<EMAIL>', password: 'Agent123!' },
      { email: '<EMAIL>', password: 'Supervisor123!' }
    ];

    for (const cred of testCredentials) {
      console.log(`Testing: ${cred.email}`);
      
      const agent = await Agent.findOne({ email: cred.email });
      if (!agent) {
        console.log('❌ Agent not found');
        continue;
      }

      const isMatch = await agent.comparePassword(cred.password);
      console.log(`✅ Password match: ${isMatch}`);
      console.log(`   Account active: ${agent.isActive}`);
      console.log(`   Account locked: ${agent.isLocked()}`);
      console.log('');
    }

    // Test password hashing
    console.log('🔐 Testing password hashing...');
    const testPassword = 'TestPassword123!';
    const hashedPassword = await bcrypt.hash(testPassword, 12);
    const isMatch = await bcrypt.compare(testPassword, hashedPassword);
    console.log(`✅ Password hashing test: ${isMatch ? 'PASSED' : 'FAILED'}`);
    console.log('');

    console.log('🎉 Database check completed!');

  } catch (error) {
    console.error('❌ Error checking database:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
}

// Run the check
checkDatabase(); 