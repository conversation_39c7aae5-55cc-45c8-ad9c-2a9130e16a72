const axios = require('axios');

// Sinch Configuration
const SINCH_CONFIG = {
  APP_KEY: '7bab0400-93ad-4afc-926f-8f1c9b2ff748',
  APP_SECRET: 'MPe7MuwHLkyspCmcR9jnXA==',
  PHONE_NUMBER: '+447418631901',
  BASE_URL: 'https://calling.api.sinch.com/calling/v1'
};

// Generate access token for Sinch API
const generateSinchToken = () => {
  const credentials = Buffer.from(`${SINCH_CONFIG.APP_KEY}:${SINCH_CONFIG.APP_SECRET}`).toString('base64');
  return credentials;
};

// Sinch API client
const sinchClient = axios.create({
  baseURL: SINCH_CONFIG.BASE_URL,
  headers: {
    'Authorization': `Basic ${generateSinchToken()}`,
    'Content-Type': 'application/json'
  }
});

// Call functions
const initiateCall = async (toNumber, fromNumber = SINCH_CONFIG.PHONE_NUMBER) => {
  try {
    const callData = {
      method: 'ttsCallout',
      ttsCallout: {
        cli: fromNumber,
        destination: {
          type: 'number',
          endpoint: toNumber
        },
        text: 'Hello, this is a call from Lead Calling App. Please wait while we connect you to an agent.',
        locale: 'en-US'
      }
    };

    const response = await sinchClient.post('/callouts', callData);
    return {
      success: true,
      callId: response.data.id,
      status: response.data.status
    };
  } catch (error) {
    console.error('Sinch call initiation error:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.text || error.message
    };
  }
};

// Get call status
const getCallStatus = async (callId) => {
  try {
    const response = await sinchClient.get(`/callouts/${callId}`);
    return {
      success: true,
      status: response.data.status,
      duration: response.data.duration,
      result: response.data.result
    };
  } catch (error) {
    console.error('Sinch call status error:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.text || error.message
    };
  }
};

// End call
const endCall = async (callId) => {
  try {
    const response = await sinchClient.delete(`/callouts/${callId}`);
    return {
      success: true,
      message: 'Call ended successfully'
    };
  } catch (error) {
    console.error('Sinch end call error:', error.response?.data || error.message);
    return {
      success: false,
      error: error.response?.data?.error?.text || error.message
    };
  }
};

module.exports = {
  SINCH_CONFIG,
  initiateCall,
  getCallStatus,
  endCall
}; 