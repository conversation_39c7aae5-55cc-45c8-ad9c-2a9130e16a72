const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  leadId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Lead',
    required: true,
    index: true
  },
  agentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Agent',
    required: false // For lead messages, this will be null
  },
  sender: {
    type: String,
    enum: ['agent', 'lead'],
    required: true
  },
  senderName: {
    type: String,
    required: true
  },
  messageType: {
    type: String,
    enum: ['text', 'image', 'audio', 'video', 'document'],
    default: 'text'
  },
  content: {
    type: String,
    required: true,
    trim: true
  },
  twilioSid: {
    type: String,
    sparse: true // Only for outgoing messages
  },
  status: {
    type: String,
    enum: ['sending', 'queued', 'sent', 'delivered', 'read', 'failed', 'undelivered'],
    default: 'sent'
  },
  direction: {
    type: String,
    enum: ['inbound', 'outbound'],
    required: true
  },
  metadata: {
    twilioStatus: String,
    errorCode: String,
    errorMessage: String,
    mediaUrl: String,
    mediaContentType: String
  },
  readAt: {
    type: Date
  },
  deliveredAt: {
    type: Date
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
messageSchema.index({ leadId: 1, createdAt: -1 });
messageSchema.index({ twilioSid: 1 });
messageSchema.index({ sender: 1, createdAt: -1 });

// Virtual for formatted timestamp
messageSchema.virtual('formattedTime').get(function() {
  return this.createdAt.toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
});

// Virtual for message age
messageSchema.virtual('isRecent').get(function() {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return this.createdAt > fiveMinutesAgo;
});

messageSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Message', messageSchema); 