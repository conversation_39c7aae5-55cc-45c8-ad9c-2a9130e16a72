const mongoose = require('mongoose');
const Agent = require('../models/Agent');

// MongoDB connection string
const MONGODB_URI = 'mongodb+srv://usman:<EMAIL>/LeadGenApp';

const seedAgents = [
  {
    email: '<EMAIL>',
    password: 'Admin123!',
    firstName: 'Admin',
    lastName: 'User',
    phone: '+1234567890',
    role: 'admin',
    isActive: true
  },
  {
    email: '<EMAIL>',
    password: 'Agent123!',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '+1234567891',
    role: 'agent',
    isActive: true
  },
  {
    email: '<EMAIL>',
    password: 'Agent123!',
    firstName: 'Sarah',
    lastName: 'Johnson',
    phone: '+1234567892',
    role: 'agent',
    isActive: true
  },
  {
    email: '<EMAIL>',
    password: 'Supervisor123!',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    phone: '+1234567893',
    role: 'supervisor',
    isActive: true
  }
];

const seedDatabase = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('Connected to MongoDB');

    // Clear existing agents
    await Agent.deleteMany({});
    console.log('Cleared existing agents');

    // Create agents individually to trigger password hashing
    const createdAgents = [];
    for (const agentData of seedAgents) {
      const agent = new Agent(agentData);
      await agent.save(); // This triggers the pre-save middleware and hashes the password
      createdAgents.push(agent);
      console.log(`Created agent: ${agent.fullName} (${agent.email})`);
    }

    console.log(`\nCreated ${createdAgents.length} agents`);

    // Display created agents (without passwords)
    createdAgents.forEach(agent => {
      console.log(`- ${agent.fullName} (${agent.email}) - Role: ${agent.role}`);
    });

    console.log('\nSeed data inserted successfully!');
    console.log('\nTest credentials:');
    console.log('Admin: <EMAIL> / Admin123!');
    console.log('Agent: <EMAIL> / Agent123!');
    console.log('Supervisor: <EMAIL> / Supervisor123!');

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    // Close connection
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
};

// Run the seed function
seedDatabase(); 