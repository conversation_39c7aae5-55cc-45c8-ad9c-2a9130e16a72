const dotenv = require('dotenv');
dotenv.config();

// Test WhatsApp API endpoints
const BASE_URL = 'http://localhost:5000';

const testWhatsAppAPI = async () => {
  console.log('🧪 Testing WhatsApp API Endpoints\n');

  // Test 1: Health check
  console.log('1️⃣ Testing Health Check...');
  try {
    const response = await fetch(`${BASE_URL}/health`);
    const data = await response.json();
    console.log('✅ Health check passed:', data.message);
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return;
  }

  // Test 2: Get a sample lead ID
  console.log('\n2️⃣ Getting sample lead...');
  let leadId = null;
  let testLead = null;
  try {
    const response = await fetch(`${BASE_URL}/api/leads`);
    const data = await response.json();
    if (data.success && data.data.length > 0) {
      testLead = data.data[0];
      leadId = testLead._id;
      console.log('✅ Found lead:', testLead.title, testLead.phone); // Use 'title' field
    } else {
      console.log('⚠️ No leads found. Creating a test lead...');
      
      const createResponse = await fetch(`${BASE_URL}/api/leads`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: 'Test WhatsApp Lead', // Use 'title' field instead of 'name'
          phone: '+**********', // Use your test phone number
          email: '<EMAIL>',
          source: 'WhatsApp Test',
          status: 'new',
          priority: 'medium'
        })
      });
      
      const createData = await createResponse.json();
      if (createData.success) {
        testLead = createData.data;
        leadId = testLead._id;
        console.log('✅ Created test lead:', testLead.title);
      } else {
        console.error('❌ Failed to create test lead:', createData);
        return;
      }
    }
  } catch (error) {
    console.error('❌ Failed to get/create lead:', error.message);
    return;
  }

  // Test 3: Send WhatsApp message
  console.log('\n3️⃣ Testing Send WhatsApp Message...');
  console.log(`   Sending to: ${testLead.title} (${testLead.phone})`);
  let messageSid = null;
  try {
    const response = await fetch(`${BASE_URL}/api/whatsapp/send`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        leadId: leadId,
        message: `Hello ${testLead.title}! 👋 This is a test message from your Lead Calling App. 📱`,
        messageType: 'text'
      })
    });

    const data = await response.json();
    console.log('📋 Full API Response:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      messageSid = data.data.messageSid;
      console.log('✅ WhatsApp message sent successfully!');
      console.log('   Message SID:', messageSid);
      console.log('   Status:', data.data.status);
      console.log('   From:', data.data.from);
      console.log('   To:', data.data.to);
      console.log('   Lead Name:', data.data.leadName);
    } else {
      console.error('❌ Failed to send WhatsApp message:', data.message);
      if (data.errorCode) {
        console.error('   Twilio Error Code:', data.errorCode);
        console.error('   More Info:', data.moreInfo);
      }
    }
  } catch (error) {
    console.error('❌ Error sending WhatsApp message:', error.message);
  }

  // Test 4: Check message status (if we have a message SID)
  if (messageSid) {
    console.log('\n4️⃣ Testing Message Status Check...');
    try {
      // Wait a moment for the message to process
      console.log('   Waiting 3 seconds for message to process...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const response = await fetch(`${BASE_URL}/api/whatsapp/status/${messageSid}`);
      const data = await response.json();
      
      if (data.success) {
        console.log('✅ Message status retrieved:');
        console.log('   SID:', data.data.sid);
        console.log('   Status:', data.data.status);
        console.log('   Created:', data.data.dateCreated);
        console.log('   Updated:', data.data.dateUpdated);
        if (data.data.errorCode) {
          console.log('   Error Code:', data.data.errorCode);
          console.log('   Error Message:', data.data.errorMessage);
        }
      } else {
        console.error('❌ Failed to get message status:', data.message);
      }
    } catch (error) {
      console.error('❌ Error checking message status:', error.message);
    }
  }

  // Test 5: Send template message (optional)
  console.log('\n5️⃣ Testing Template Message (optional)...');
  try {
    const response = await fetch(`${BASE_URL}/api/whatsapp/template`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        leadId: leadId,
        templateName: 'hello_world' // This should be your approved template SID
      })
    });

    const data = await response.json();
    if (data.success) {
      console.log('✅ Template message sent successfully!');
      console.log('   Message SID:', data.data.messageSid);
      console.log('   Template:', data.data.template);
    } else {
      console.log('⚠️ Template message failed (expected if no approved templates):', data.message);
      if (data.errorCode) {
        console.log('   Error Code:', data.errorCode);
      }
    }
  } catch (error) {
    console.log('⚠️ Template message test error (expected):', error.message);
  }

  console.log('\n🎉 WhatsApp API tests completed!');
  console.log('\n📋 Next Steps:');
  console.log('   1. Make sure your Twilio WhatsApp number is properly configured');
  console.log('   2. Update the .env file with your actual Twilio credentials');
  console.log('   3. Test with your own phone number (update the lead phone number)');
  console.log('   4. Join the Twilio WhatsApp sandbox if using the sandbox number');
  console.log('   5. Set up WhatsApp Business API for production use');
};

// Configuration check
const checkConfiguration = () => {
  console.log('🔧 Checking Twilio Configuration...\n');
  
  const requiredVars = [
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN',
    'TWILIO_WHATSAPP_FROM'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingVars.forEach(varName => console.error(`   - ${varName}`));
    console.error('\n💡 Please set these in your .env file:');
    console.error('   TWILIO_ACCOUNT_SID=your_account_sid');
    console.error('   TWILIO_AUTH_TOKEN=your_auth_token');
    console.error('   TWILIO_WHATSAPP_FROM=whatsapp:+***********');
    return false;
  }
  
  console.log('✅ All required environment variables are set');
  console.log(`   Account SID: ${process.env.TWILIO_ACCOUNT_SID.substring(0, 10)}...`);
  console.log(`   WhatsApp From: ${process.env.TWILIO_WHATSAPP_FROM}`);
  
  // Additional validation
  if (!process.env.TWILIO_WHATSAPP_FROM.startsWith('whatsapp:')) {
    console.warn('⚠️ TWILIO_WHATSAPP_FROM should start with "whatsapp:" prefix');
  }
  
  return true;
};

// Phone number validation helper
const validatePhoneNumber = (phoneNumber) => {
  // Basic validation for international format
  const phoneRegex = /^\+[1-9]\d{1,14}$/;
  return phoneRegex.test(phoneNumber);
};

// Main execution
if (require.main === module) {
  if (checkConfiguration()) {
    testWhatsAppAPI().catch(error => {
      console.error('💥 Test script failed:', error);
      process.exit(1);
    });
  }
}

module.exports = { testWhatsAppAPI, checkConfiguration, validatePhoneNumber }; 