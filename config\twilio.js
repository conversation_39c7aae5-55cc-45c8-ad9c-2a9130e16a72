const twilio = require('twilio');

// Twilio Configuration
const TWILIO_CONFIG = {
  ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
  AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
  WHATSAPP_FROM: process.env.TWILIO_WHATSAPP_FROM, // Format: whatsapp:+**********
  PHONE_FROM: process.env.TWILIO_PHONE_FROM // Your Twilio phone number
};

// Validate Twilio configuration
const validateTwilioConfig = () => {
  const requiredFields = ['ACCOUNT_SID', 'AUTH_TOKEN', 'WHATSAPP_FROM'];
  const missingFields = requiredFields.filter(field => !TWILIO_CONFIG[field]);
  
  if (missingFields.length > 0) {
    console.error('❌ Missing required Twilio configuration fields:', missingFields);
    return false;
  }
  
  console.log('✅ Twilio configuration loaded successfully');
  return true;
};

// Initialize Twilio client
let twilioClient = null;

const initializeTwilioClient = () => {
  try {
    if (!validateTwilioConfig()) {
      throw new Error('Invalid Twilio configuration');
    }

    twilioClient = twilio(TWILIO_CONFIG.ACCOUNT_SID, TWILIO_CONFIG.AUTH_TOKEN);
    console.log('✅ Twilio client initialized successfully');
    return twilioClient;
  } catch (error) {
    console.error('❌ Failed to initialize Twilio client:', error.message);
    throw error;
  }
};

// Get Twilio client instance
const getTwilioClient = () => {
  if (!twilioClient) {
    return initializeTwilioClient();
  }
  return twilioClient;
};

// Format phone number for WhatsApp
const formatWhatsAppNumber = (phoneNumber) => {
  // Remove any existing whatsapp: prefix
  let cleanNumber = phoneNumber.replace(/^whatsapp:/, '');
  
  // Ensure number starts with +
  if (!cleanNumber.startsWith('+')) {
    cleanNumber = '+' + cleanNumber;
  }
  
  return `whatsapp:${cleanNumber}`;
};

module.exports = {
  TWILIO_CONFIG,
  getTwilioClient,
  initializeTwilioClient,
  validateTwilioConfig,
  formatWhatsAppNumber
}; 