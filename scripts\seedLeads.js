const mongoose = require('mongoose');
const Lead = require('../models/Lead');

const MONGODB_URI = 'mongodb+srv://usman:<EMAIL>/LeadGenApp';

const seedLeads = [
  {
    title: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    status: 'new',
    priority: 'high',
    source: 'Website',
    notes: 'Interested in product demo.',
    capturedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
  },
  {
    title: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567891',
    status: 'contacted',
    priority: 'medium',
    source: 'Referral',
    notes: 'Requested pricing details.',
    capturedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
  },
  {
    title: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567892',
    status: 'qualified',
    priority: 'low',
    source: 'Ad Campaign',
    notes: 'Follow up next week.',
    capturedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
  },
  {
    title: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567893',
    status: 'new',
    priority: 'medium',
    source: 'Event',
    notes: 'Met at trade show.',
    capturedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  }
];

const seedDatabase = async () => {
  try {
    await mongoose.connect(MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');
    await Lead.deleteMany({});
    console.log('Cleared existing leads');
    const createdLeads = await Lead.insertMany(seedLeads);
    console.log(`Created ${createdLeads.length} leads`);
    createdLeads.forEach(lead => {
      console.log(`- ${lead.title} (${lead.email}) - Status: ${lead.status}`);
    });
    console.log('\nSeed data inserted successfully!');
  } catch (error) {
    console.error('Error seeding leads:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
    process.exit(0);
  }
};

seedDatabase(); 