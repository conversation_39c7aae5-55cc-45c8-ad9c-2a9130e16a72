const express = require('express');
const router = express.Router();
const {
  initiateCallToLead,
  getCallStatusById,
  endCallById,
  getCallHistory
} = require('../controllers/callController');

// POST /api/calls/initiate - Initiate call to lead
router.post('/initiate', initiateCallToLead);

// GET /api/calls/:callId/status - Get call status
router.get('/:callId/status', getCallStatusById);

// DELETE /api/calls/:callId - End call
router.delete('/:callId', endCallById);

// GET /api/calls/lead/:leadId - Get call history for lead
router.get('/lead/:leadId', getCallHistory);

module.exports = router; 