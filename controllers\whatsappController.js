const { getTwilioClient, TWILIO_CONFIG, formatWhatsAppNumber } = require('../config/twilio');
const Lead = require('../models/Lead');
const Message = require('../models/Message');

// Send WhatsApp message to lead
const sendWhatsAppMessage = async (req, res) => {
  try {
    const { leadId, message, messageType = 'text', agentId = null } = req.body;
    
    if (!leadId || !message) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID and message are required'
      });
    }

    console.log('📱 Sending WhatsApp message to lead:', leadId);

    // Get lead details
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    // Save message to database first
    const messageDoc = new Message({
      leadId: leadId,
      agentId: agentId,
      sender: 'agent',
      senderName: 'Agent', // You can get actual agent name from agentId
      messageType: messageType,
      content: message,
      direction: 'outbound',
      status: 'sending'
    });

    await messageDoc.save();

    // Get Twilio client
    const client = getTwilioClient();
    
    // Format phone numbers for WhatsApp
    const fromNumber = TWILIO_CONFIG.WHATSAPP_FROM;
    const toNumber = formatWhatsAppNumber(lead.phone);
    
    console.log('📱 WhatsApp details:', {
      leadName: lead.title,
      from: fromNumber,
      to: toNumber,
      messageLength: message.length
    });

    try {
      // Send WhatsApp message
      const twilioMessage = await client.messages.create({
        from: fromNumber,
        to: toNumber,
        body: message
      });

      // Update message with Twilio SID and status
      messageDoc.twilioSid = twilioMessage.sid;
      messageDoc.status = twilioMessage.status || 'sent';
      messageDoc.metadata = {
        twilioStatus: twilioMessage.status
      };
      await messageDoc.save();

      console.log('✅ WhatsApp message sent successfully:', twilioMessage.sid);

      // Emit real-time update to connected clients
      const io = req.app.get('io');
      if (io) {
        io.to(`lead_${leadId}`).emit('new_message', {
          _id: messageDoc._id,
          content: messageDoc.content,
          sender: messageDoc.sender,
          senderName: messageDoc.senderName,
          status: messageDoc.status,
          createdAt: messageDoc.createdAt,
          twilioSid: messageDoc.twilioSid
        });
      }

      res.json({
        success: true,
        data: {
          messageId: messageDoc._id,
          messageSid: twilioMessage.sid,
          status: twilioMessage.status,
          from: fromNumber,
          to: toNumber,
          messageBody: message,
          leadId: leadId,
          leadName: lead.title,
          sentAt: messageDoc.createdAt
        },
        message: 'WhatsApp message sent successfully'
      });

    } catch (twilioError) {
      // Update message status to failed
      messageDoc.status = 'failed';
      messageDoc.metadata = {
        errorCode: twilioError.code,
        errorMessage: twilioError.message
      };
      await messageDoc.save();
      
      throw twilioError;
    }

  } catch (error) {
    console.error('❌ Error sending WhatsApp message:', error);
    console.error('❌ Error details:', {
      message: error.message,
      code: error.code,
      moreInfo: error.moreInfo,
      status: error.status,
      details: error.details
    });
    
    // Handle specific Twilio errors
    if (error.code) {
      return res.status(400).json({
        success: false,
        message: `Twilio error: ${error.message}`,
        errorCode: error.code,
        moreInfo: error.moreInfo
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to send WhatsApp message',
      error: error.message
    });
  }
};

// Get chat messages for a lead
const getChatMessages = async (req, res) => {
  try {
    const { leadId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    if (!leadId) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID is required'
      });
    }

    // Get messages with pagination
    const messages = await Message.find({ leadId })
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('leadId', 'title phone')
      .lean();

    // Reverse to show oldest first
    messages.reverse();

    // Get total count for pagination
    const total = await Message.countDocuments({ leadId });

    res.json({
      success: true,
      data: {
        messages,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalMessages: total,
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('❌ Error fetching chat messages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch chat messages',
      error: error.message
    });
  }
};

// Mark messages as read
const markMessagesAsRead = async (req, res) => {
  try {
    const { leadId } = req.params;
    const { messageIds } = req.body;

    if (!leadId) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID is required'
      });
    }

    const query = { leadId, sender: 'lead' };
    if (messageIds && messageIds.length > 0) {
      query._id = { $in: messageIds };
    }

    const result = await Message.updateMany(
      query,
      { 
        $set: { 
          status: 'read', 
          readAt: new Date() 
        } 
      }
    );

    // Emit real-time update
    const io = req.app.get('io');
    if (io) {
      io.to(`lead_${leadId}`).emit('messages_read', {
        leadId,
        readAt: new Date(),
        count: result.modifiedCount
      });
    }

    res.json({
      success: true,
      data: {
        updatedCount: result.modifiedCount
      },
      message: 'Messages marked as read'
    });

  } catch (error) {
    console.error('❌ Error marking messages as read:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark messages as read',
      error: error.message
    });
  }
};

// Send template WhatsApp message (for opt-in purposes)
const sendTemplateMessage = async (req, res) => {
  try {
    const { leadId, templateName = 'hello_world' } = req.body;
    
    if (!leadId) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID is required'
      });
    }

    // Get lead details
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    const client = getTwilioClient();
    const fromNumber = TWILIO_CONFIG.WHATSAPP_FROM;
    const toNumber = formatWhatsAppNumber(lead.phone);

    // Send template message (this is typically used for initial opt-in)
    const twilioMessage = await client.messages.create({
      from: fromNumber,
      to: toNumber,
      contentSid: templateName, // Use your approved template SID
      contentVariables: JSON.stringify({
        "1": lead.title || "there" // Use 'title' field and provide fallback
      })
    });

    console.log('✅ WhatsApp template message sent:', twilioMessage.sid);

    res.json({
      success: true,
      data: {
        messageSid: twilioMessage.sid,
        status: twilioMessage.status,
        template: templateName,
        leadId: leadId
      },
      message: 'WhatsApp template message sent successfully'
    });

  } catch (error) {
    console.error('❌ Error sending WhatsApp template:', error);
    console.error('❌ Template error details:', {
      message: error.message,
      code: error.code,
      moreInfo: error.moreInfo
    });
    res.status(500).json({
      success: false,
      message: 'Failed to send WhatsApp template message',
      error: error.message,
      errorCode: error.code
    });
  }
};

// Get WhatsApp message status
const getMessageStatus = async (req, res) => {
  try {
    const { messageSid } = req.params;
    
    if (!messageSid) {
      return res.status(400).json({
        success: false,
        message: 'Message SID is required'
      });
    }

    const client = getTwilioClient();
    const message = await client.messages(messageSid).fetch();

    // Update local message status
    await Message.findOneAndUpdate(
      { twilioSid: messageSid },
      { 
        status: message.status,
        'metadata.twilioStatus': message.status,
        deliveredAt: message.status === 'delivered' ? new Date() : undefined
      }
    );

    res.json({
      success: true,
      data: {
        sid: message.sid,
        status: message.status,
        errorCode: message.errorCode,
        errorMessage: message.errorMessage,
        dateCreated: message.dateCreated,
        dateUpdated: message.dateUpdated,
        dateSent: message.dateSent
      }
    });

  } catch (error) {
    console.error('❌ Error fetching message status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch message status',
      error: error.message
    });
  }
};

// Enhanced webhook handler for WhatsApp message status updates and incoming messages
const handleWebhook = async (req, res) => {
  try {
    const { 
      MessageSid, 
      MessageStatus, 
      From, 
      To, 
      Body,
      MediaUrl0,
      MediaContentType0,
      NumMedia 
    } = req.body;
    
    console.log('📱 WhatsApp webhook received:', {
      messageSid: MessageSid,
      status: MessageStatus,
      from: From,
      to: To,
      body: Body,
      hasMedia: NumMedia > 0
    });

    // Handle status updates for outgoing messages
    if (MessageSid && MessageStatus) {
      const message = await Message.findOneAndUpdate(
        { twilioSid: MessageSid },
        { 
          status: MessageStatus,
          'metadata.twilioStatus': MessageStatus,
          deliveredAt: MessageStatus === 'delivered' ? new Date() : undefined,
          readAt: MessageStatus === 'read' ? new Date() : undefined
        },
        { new: true }
      );

      if (message) {
        // Emit real-time status update
        const io = req.app.get('io');
        if (io) {
          io.to(`lead_${message.leadId}`).emit('message_status_update', {
            messageId: message._id,
            twilioSid: MessageSid,
            status: MessageStatus,
            updatedAt: new Date()
          });
        }
      }
    }

    // Handle incoming messages from leads
    if (Body && From && From.includes('whatsapp:')) {
      const phoneNumber = From.replace('whatsapp:', '');
      
      // Find lead by phone number
      const lead = await Lead.findOne({ 
        phone: { $regex: phoneNumber.replace('+', '\\+'), $options: 'i' }
      });

      if (lead) {
        // Save incoming message
        const incomingMessage = new Message({
          leadId: lead._id,
          sender: 'lead',
          senderName: lead.title,
          messageType: NumMedia > 0 ? 'image' : 'text', // Simplified media detection
          content: Body || '[Media message]',
          direction: 'inbound',
          status: 'delivered',
          twilioSid: MessageSid,
          metadata: {
            mediaUrl: MediaUrl0,
            mediaContentType: MediaContentType0,
            twilioStatus: 'received'
          }
        });

        await incomingMessage.save();

        // Emit real-time update to connected agents
        const io = req.app.get('io');
        if (io) {
          io.to(`lead_${lead._id}`).emit('new_message', {
            _id: incomingMessage._id,
            content: incomingMessage.content,
            sender: incomingMessage.sender,
            senderName: incomingMessage.senderName,
            messageType: incomingMessage.messageType,
            status: incomingMessage.status,
            createdAt: incomingMessage.createdAt,
            metadata: incomingMessage.metadata
          });

          // Notify all agents about new lead message
          io.emit('lead_message_notification', {
            leadId: lead._id,
            leadName: lead.title,
            message: Body,
            timestamp: incomingMessage.createdAt
          });
        }

        console.log('✅ Incoming message saved:', incomingMessage._id);
      } else {
        console.warn('⚠️ Received message from unknown number:', phoneNumber);
      }
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('❌ Error handling WhatsApp webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to handle webhook',
      error: error.message
    });
  }
};

module.exports = {
  sendWhatsAppMessage,
  getChatMessages,
  markMessagesAsRead,
  sendTemplateMessage,
  getMessageStatus,
  handleWebhook
}; 