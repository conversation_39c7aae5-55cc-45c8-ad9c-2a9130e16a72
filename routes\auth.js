const express = require('express');
const router = express.Router();
const {
  signIn,
  register,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  logout
} = require('../controllers/authController');
const { protect } = require('../middleware/auth');
const {
  validateSignIn,
  validateRegistration,
  validatePasswordReset,
  validatePasswordChange
} = require('../middleware/validation');

// Public routes
router.post('/signin', validateSignIn, signIn);
router.post('/register', validateRegistration, register);
router.post('/forgot-password', validatePasswordReset, forgotPassword);

// Protected routes
router.get('/profile', protect, getProfile);
router.put('/profile', protect, updateProfile);
router.put('/change-password', protect, validatePasswordChange, changePassword);
router.post('/logout', protect, logout);

module.exports = router; 