# WhatsApp Integration Setup

This guide explains how to set up Twilio WhatsApp integration for sending messages to leads.

## 🔧 Prerequisites

1. **Twilio Account**: Sign up at [https://www.twilio.com](https://www.twilio.com)
2. **WhatsApp Business API**: Request access through Twilio Console
3. **Node.js Backend**: Our Express.js server with WhatsApp endpoints

## 📱 Twilio WhatsApp Setup

### Step 1: Get Twilio Credentials

1. Log into your [Twilio Console](https://console.twilio.com)
2. Navigate to **Account Dashboard**
3. Copy your **Account SID** and **Auth Token**
4. Go to **Develop** > **Phone Numbers** > **Manage** > **WhatsApp senders**

### Step 2: WhatsApp Sandbox (Development)

For development/testing, use Twilio's WhatsApp Sandbox:

1. In Twilio Console, go to **Develop** > **Messaging** > **Try it out** > **Send a WhatsApp message**
2. Follow the instructions to join the sandbox
3. Send `join <your-sandbox-code>` to the Twilio WhatsApp number
4. Use the sandbox number: `whatsapp:+***********`

### Step 3: WhatsApp Business API (Production)

For production use:

1. Submit WhatsApp Business API request through Twilio
2. Get your business verified
3. Set up approved message templates
4. Use your approved WhatsApp Business number

## 🔐 Environment Configuration

Create/update your `.env` file:

```env
# Twilio WhatsApp Configuration
TWILIO_ACCOUNT_SID=AC**********abcdef**********abcdef
TWILIO_AUTH_TOKEN=your_auth_token_32_characters_long
TWILIO_WHATSAPP_FROM=whatsapp:+***********
TWILIO_PHONE_FROM=+**********

# Example for production:
# TWILIO_WHATSAPP_FROM=whatsapp:+**********
```

## 🚀 API Endpoints

### Send WhatsApp Message

```bash
POST /api/whatsapp/send
Content-Type: application/json

{
  "leadId": "lead_id_here",
  "message": "Hello! This is a test message.",
  "messageType": "text"
}
```

### Send Template Message

```bash
POST /api/whatsapp/template
Content-Type: application/json

{
  "leadId": "lead_id_here",
  "templateName": "hello_world"
}
```

### Check Message Status

```bash
GET /api/whatsapp/status/{messageSid}
```

### Webhook Handler

```bash
POST /api/whatsapp/webhook
```

## 🧪 Testing

### 1. Install Dependencies

```bash
npm install twilio
```

### 2. Test Configuration

```bash
npm run test-whatsapp
```

### 3. Manual API Testing

```bash
# Test sending a message
curl -X POST http://localhost:5000/api/whatsapp/send \
  -H "Content-Type: application/json" \
  -d '{
    "leadId": "your_lead_id",
    "message": "Hello from API test!"
  }'
```

## 📋 Message Status Tracking

WhatsApp messages go through these states:

- **`queued`**: Message is queued for sending
- **`sent`**: Message sent to WhatsApp
- **`delivered`**: Message delivered to recipient
- **`read`**: Message read by recipient (if read receipts enabled)
- **`failed`**: Message failed to send

## 🔄 Webhook Configuration

To receive message status updates and incoming messages:

1. Set up a public webhook URL (use ngrok for local development)
2. Configure webhook in Twilio Console
3. Point to: `https://your-domain.com/api/whatsapp/webhook`

### Example Webhook Setup with ngrok

```bash
# Install ngrok
npm install -g ngrok

# Expose local server
ngrok http 5000

# Use the HTTPS URL in Twilio webhook configuration
```

## 📱 React Native Integration

The ChatScreen component automatically:

1. Fetches lead details from the backend
2. Sends WhatsApp messages via `/api/whatsapp/send`
3. Shows message status with icons
4. Handles errors gracefully

### Usage in App

```javascript
// Navigate to ChatScreen with lead object
navigation.navigate('Chat', { lead: leadObject });
```

## 🚨 Error Handling

Common errors and solutions:

### Authentication Errors
- **Error 20003**: Invalid auth credentials
- **Solution**: Check `TWILIO_ACCOUNT_SID` and `TWILIO_AUTH_TOKEN`

### WhatsApp Errors
- **Error 63016**: Invalid WhatsApp number format
- **Solution**: Use format `whatsapp:+**********`

### Rate Limiting
- **Error 20429**: Too many requests
- **Solution**: Implement rate limiting in your app

### Sandbox Limitations
- **Error 63007**: User hasn't joined sandbox
- **Solution**: Recipient must send "join sandbox-code" first

## 🔒 Security Best Practices

1. **Environment Variables**: Never commit `.env` files
2. **Rate Limiting**: Implement API rate limiting
3. **Input Validation**: Validate phone numbers and message content
4. **Webhook Security**: Verify webhook signatures
5. **HTTPS**: Use HTTPS for webhook endpoints

## 📊 Production Checklist

- [ ] Twilio account upgraded (not trial)
- [ ] WhatsApp Business API approved
- [ ] Message templates approved
- [ ] Webhook URL configured
- [ ] Rate limiting implemented
- [ ] Error handling in place
- [ ] Phone number validation
- [ ] Monitoring and logging set up

## 🆘 Support

- **Twilio Docs**: [https://www.twilio.com/docs/whatsapp](https://www.twilio.com/docs/whatsapp)
- **WhatsApp Business API**: [https://developers.facebook.com/docs/whatsapp](https://developers.facebook.com/docs/whatsapp)
- **Twilio Support**: Available through Twilio Console

## 🎯 Next Steps

1. Set up your Twilio credentials in `.env`
2. Run `npm run test-whatsapp` to verify setup
3. Test with the React Native ChatScreen
4. Configure webhooks for production
5. Submit for WhatsApp Business API approval 