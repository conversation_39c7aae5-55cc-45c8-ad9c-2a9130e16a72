const Agent = require('../models/Agent');
const { generateToken } = require('../middleware/auth');

// @desc    Agent sign in
// @route   POST /api/auth/signin
// @access  Public
const signIn = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    console.log('🔐 Sign in attempt:', { email, hasPassword: !!password });
    console.log('📝 Request body:', req.body);

    // Check if agent exists
    const agent = await Agent.findOne({ email });
    console.log('👤 Agent found:', agent ? 'Yes' : 'No');
    
    if (!agent) {
      console.log('❌ Agent not found for email:', email);
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    console.log('🔍 Agent details:', {
      id: agent._id,
      email: agent.email,
      isActive: agent.isActive,
      loginAttempts: agent.loginAttempts,
      isLocked: agent.isLocked()
    });

    // Check if account is locked
    if (agent.isLocked()) {
      console.log('🔒 Account is locked for:', email);
      return res.status(423).json({
        success: false,
        message: 'Account is temporarily locked due to too many failed login attempts. Please try again later.'
      });
    }

    // Check if account is active
    if (!agent.isActive) {
      console.log('🚫 Account is deactivated for:', email);
      return res.status(401).json({
        success: false,
        message: 'Account is deactivated. Please contact administrator.'
      });
    }

    // Check password
    console.log('🔑 Checking password...');
    const isMatch = await agent.comparePassword(password);
    console.log('🔑 Password match:', isMatch);
    
    if (!isMatch) {
      console.log('❌ Password mismatch for:', email);
      // Increment login attempts
      await agent.incLoginAttempts();
      console.log('📈 Login attempts incremented');
      
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Reset login attempts on successful login
    console.log('✅ Password correct, resetting login attempts');
    await Agent.resetLoginAttempts(email);

    // Update last login
    agent.lastLogin = new Date();
    await agent.save();
    console.log('📅 Last login updated');

    // Generate token
    const token = generateToken(agent._id);
    console.log('🎫 JWT token generated');

    const responseData = {
      success: true,
      message: 'Sign in successful',
      data: {
        agent: {
          id: agent._id,
          email: agent.email,
          firstName: agent.firstName,
          lastName: agent.lastName,
          fullName: agent.fullName,
          role: agent.role,
          phone: agent.phone,
          lastLogin: agent.lastLogin
        },
        token
      }
    };

    console.log('✅ Sign in successful for:', email);
    console.log('📤 Sending response:', JSON.stringify(responseData, null, 2));

    res.status(200).json(responseData);

  } catch (error) {
    console.error('💥 Sign in error:', error);
    console.error('Stack trace:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Agent registration
// @route   POST /api/auth/register
// @access  Public
const register = async (req, res) => {
  try {
    const { email, password, firstName, lastName, phone } = req.body;

    // Check if agent already exists
    const existingAgent = await Agent.findOne({ email });
    if (existingAgent) {
      return res.status(400).json({
        success: false,
        message: 'Agent with this email already exists'
      });
    }

    // Create new agent
    const agent = await Agent.create({
      email,
      password,
      firstName,
      lastName,
      phone
    });

    // Generate token
    const token = generateToken(agent._id);

    res.status(201).json({
      success: true,
      message: 'Agent registered successfully',
      data: {
        agent: {
          id: agent._id,
          email: agent.email,
          firstName: agent.firstName,
          lastName: agent.lastName,
          fullName: agent.fullName,
          role: agent.role,
          phone: agent.phone
        },
        token
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    
    // Handle duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Agent with this email already exists'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get current agent profile
// @route   GET /api/auth/profile
// @access  Private
const getProfile = async (req, res) => {
  try {
    const agent = await Agent.findById(req.agent._id);
    
    res.status(200).json({
      success: true,
      data: {
        agent: {
          id: agent._id,
          email: agent.email,
          firstName: agent.firstName,
          lastName: agent.lastName,
          fullName: agent.fullName,
          role: agent.role,
          phone: agent.phone,
          lastLogin: agent.lastLogin,
          createdAt: agent.createdAt
        }
      }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update agent profile
// @route   PUT /api/auth/profile
// @access  Private
const updateProfile = async (req, res) => {
  try {
    const { firstName, lastName, phone } = req.body;

    const agent = await Agent.findById(req.agent._id);
    if (!agent) {
      return res.status(404).json({
        success: false,
        message: 'Agent not found'
      });
    }

    // Update fields
    agent.firstName = firstName || agent.firstName;
    agent.lastName = lastName || agent.lastName;
    agent.phone = phone || agent.phone;

    await agent.save();

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        agent: {
          id: agent._id,
          email: agent.email,
          firstName: agent.firstName,
          lastName: agent.lastName,
          fullName: agent.fullName,
          role: agent.role,
          phone: agent.phone,
          lastLogin: agent.lastLogin
        }
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Change password
// @route   PUT /api/auth/change-password
// @access  Private
const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;

    const agent = await Agent.findById(req.agent._id);
    if (!agent) {
      return res.status(404).json({
        success: false,
        message: 'Agent not found'
      });
    }

    // Verify current password
    const isMatch = await agent.comparePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Update password
    agent.password = newPassword;
    await agent.save();

    res.status(200).json({
      success: true,
      message: 'Password changed successfully'
    });

  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Request password reset
// @route   POST /api/auth/forgot-password
// @access  Public
const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    const agent = await Agent.findOne({ email });
    if (!agent) {
      // Don't reveal if email exists or not for security
      return res.status(200).json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent'
      });
    }

    // Generate reset token (in a real app, you'd send this via email)
    const resetToken = generateToken(agent._id);

    // For demo purposes, we'll just return success
    // In production, you'd send an email with the reset link
    res.status(200).json({
      success: true,
      message: 'Password reset link sent to your email',
      data: {
        resetToken // Remove this in production
      }
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Logout agent
// @route   POST /api/auth/logout
// @access  Private
const logout = async (req, res) => {
  try {
    // In a real application, you might want to blacklist the token
    // For now, we'll just return success
    res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });

  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  signIn,
  register,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  logout
}; 