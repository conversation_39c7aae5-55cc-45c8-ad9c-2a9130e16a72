const dotenv = require('dotenv');
dotenv.config();

// Import our Twilio configuration
const { TWILIO_CONFIG, validateTwilioConfig, getTwilioClient } = require('./config/twilio');

console.log('🔍 Debugging Twilio Configuration\n');

// Step 1: Check environment variables
console.log('1️⃣ Environment Variables:');
console.log('   NODE_ENV:', process.env.NODE_ENV);
console.log('   TWILIO_ACCOUNT_SID:', process.env.TWILIO_ACCOUNT_SID ? `${process.env.TWILIO_ACCOUNT_SID.substring(0, 10)}...` : 'NOT SET');
console.log('   TWILIO_AUTH_TOKEN:', process.env.TWILIO_AUTH_TOKEN ? `${process.env.TWILIO_AUTH_TOKEN.substring(0, 6)}...` : 'NOT SET');
console.log('   TWILIO_WHATSAPP_FROM:', process.env.TWILIO_WHATSAPP_FROM || 'NOT SET');
console.log('   TWILIO_PHONE_FROM:', process.env.TWILIO_PHONE_FROM || 'NOT SET');

// Step 2: Check TWILIO_CONFIG object
console.log('\n2️⃣ TWILIO_CONFIG Object:');
console.log('   ACCOUNT_SID:', TWILIO_CONFIG.ACCOUNT_SID ? `${TWILIO_CONFIG.ACCOUNT_SID.substring(0, 10)}...` : 'NOT SET');
console.log('   AUTH_TOKEN:', TWILIO_CONFIG.AUTH_TOKEN ? `${TWILIO_CONFIG.AUTH_TOKEN.substring(0, 6)}...` : 'NOT SET');
console.log('   WHATSAPP_FROM:', TWILIO_CONFIG.WHATSAPP_FROM || 'NOT SET');
console.log('   PHONE_FROM:', TWILIO_CONFIG.PHONE_FROM || 'NOT SET');

// Step 3: Test validation
console.log('\n3️⃣ Configuration Validation:');
try {
  const isValid = validateTwilioConfig();
  console.log('   Validation result:', isValid ? '✅ VALID' : '❌ INVALID');
} catch (error) {
  console.log('   Validation error:', error.message);
}

// Step 4: Test Twilio client initialization
console.log('\n4️⃣ Twilio Client Initialization:');
try {
  const client = getTwilioClient();
  console.log('   Client initialization: ✅ SUCCESS');
  console.log('   Client object type:', typeof client);
  console.log('   Client has messages:', typeof client.messages);
} catch (error) {
  console.log('   Client initialization: ❌ FAILED');
  console.log('   Error:', error.message);
  console.log('   Stack:', error.stack);
}

// Step 5: Test a simple Twilio API call (account info)
console.log('\n5️⃣ Testing Twilio API Connection:');
try {
  const client = getTwilioClient();
  
  // Test account fetch (this doesn't send messages, just checks auth)
  client.api.accounts(TWILIO_CONFIG.ACCOUNT_SID).fetch()
    .then(account => {
      console.log('   API Test: ✅ SUCCESS');
      console.log('   Account SID:', account.sid);
      console.log('   Account Status:', account.status);
      console.log('   Account Type:', account.type);
    })
    .catch(error => {
      console.log('   API Test: ❌ FAILED');
      console.log('   Error Code:', error.code);
      console.log('   Error Message:', error.message);
      console.log('   More Info:', error.moreInfo);
    });
    
} catch (error) {
  console.log('   API Test: ❌ FAILED (Client not initialized)');
  console.log('   Error:', error.message);
}

// Step 6: Test phone number formatting
console.log('\n6️⃣ Phone Number Formatting Test:');
const { formatWhatsAppNumber } = require('./config/twilio');
const testNumbers = ['+************', '************', 'whatsapp:+************'];
testNumbers.forEach(number => {
  try {
    const formatted = formatWhatsAppNumber(number);
    console.log(`   ${number} → ${formatted}`);
  } catch (error) {
    console.log(`   ${number} → ERROR: ${error.message}`);
  }
});

console.log('\n🏁 Debug completed!'); 