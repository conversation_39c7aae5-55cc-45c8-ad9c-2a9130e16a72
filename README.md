# Lead Generation App - Backend

A Node.js backend server for the Lead Generation App with MongoDB integration and JWT authentication.

## Features

- 🔐 JWT Authentication
- 👤 Agent Management
- 🔒 Password Security (bcrypt)
- 🛡️ Rate Limiting
- ✅ Input Validation
- 📝 Comprehensive Error Handling
- 🔄 CORS Support
- 📊 MongoDB Integration

## Prerequisites

- Node.js (v14 or higher)
- MongoDB Atlas account
- npm or yarn

## Installation

1. **Clone the repository and navigate to backend directory:**
   ```bash
   cd backend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   Create a `.env` file in the root directory with the following variables:
   ```env
   MONGODB_URI=mongodb+srv://usman:<EMAIL>/LeadGenApp
   JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
   PORT=5000
   NODE_ENV=development
   ```

4. **Seed the database with initial data:**
   ```bash
   node scripts/seedData.js
   ```

5. **Start the server:**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## API Endpoints

### Authentication Routes

#### POST `/api/auth/signin`
Agent sign in.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Agent123!"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Sign in successful",
  "data": {
    "agent": {
      "id": "agent_id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Smith",
      "fullName": "John Smith",
      "role": "agent",
      "phone": "+1234567891",
      "lastLogin": "2024-01-15T10:30:00.000Z"
    },
    "token": "jwt_token_here"
  }
}
```

#### POST `/api/auth/register`
Register a new agent.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "NewAgent123!",
  "firstName": "Jane",
  "lastName": "Doe",
  "phone": "+1234567894"
}
```

#### POST `/api/auth/forgot-password`
Request password reset.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

#### GET `/api/auth/profile`
Get current agent profile (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

#### PUT `/api/auth/profile`
Update agent profile (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "firstName": "Updated",
  "lastName": "Name",
  "phone": "+1234567899"
}
```

#### PUT `/api/auth/change-password`
Change password (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewPassword123!"
}
```

#### POST `/api/auth/logout`
Logout agent (requires authentication).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

## Test Credentials

After running the seed script, you can use these test credentials:

- **Admin:** <EMAIL> / Admin123!
- **Agent 1:** <EMAIL> / Agent123!
- **Agent 2:** <EMAIL> / Agent123!
- **Supervisor:** <EMAIL> / Supervisor123!

## Security Features

- **Password Hashing:** All passwords are hashed using bcrypt
- **JWT Tokens:** Secure authentication with JSON Web Tokens
- **Rate Limiting:** Prevents brute force attacks
- **Input Validation:** Comprehensive validation for all inputs
- **Account Locking:** Accounts are locked after 5 failed login attempts
- **CORS Protection:** Configured for secure cross-origin requests
- **Helmet:** Security headers for protection against common vulnerabilities

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "email",
      "message": "Please enter a valid email address"
    }
  ]
}
```

## Development

### Project Structure
```
backend/
├── config/
│   └── database.js          # MongoDB connection
├── controllers/
│   └── authController.js    # Authentication logic
├── middleware/
│   ├── auth.js             # JWT authentication
│   ├── validation.js       # Input validation
│   └── errorHandler.js     # Error handling
├── models/
│   └── Agent.js            # Agent model
├── routes/
│   └── auth.js             # Authentication routes
├── scripts/
│   └── seedData.js         # Database seeding
├── server.js               # Main server file
└── package.json
```

### Available Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `node scripts/seedData.js` - Seed database with test data

## Health Check

Check if the server is running:
```bash
GET http://localhost:5000/health
```

Response:
```json
{
  "success": true,
  "message": "Server is running",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "environment": "development"
}
```

## Integration with React Native

To connect your React Native app to this backend:

1. Update your React Native app to use the backend API endpoints
2. Store the JWT token securely (e.g., using AsyncStorage)
3. Include the token in the Authorization header for protected requests
4. Handle authentication errors and token expiration

Example API call from React Native:
```javascript
const signIn = async (email, password) => {
  try {
    const response = await fetch('http://localhost:5000/api/auth/signin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Sign in error:', error);
    throw error;
  }
};
```

## License

MIT 