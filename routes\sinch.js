const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { SINCH_CONFIG } = require('../config/sinch');

// POST /api/sinch/token
router.post('/token', (req, res) => {
  try {
    const { userId } = req.body;
    
    if (!userId) {
      return res.status(400).json({ 
        success: false, 
        message: 'userId is required' 
      });
    }

    console.log('🔐 Generating Sinch token for user:', userId);

    // Decode the base64 secret
    const secret = Buffer.from(SINCH_CONFIG.APP_SECRET, 'base64');

    // Sinch JWT payload for Voice SDK
    const payload = {
      iss: SINCH_CONFIG.APP_KEY,
      sub: userId,
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours expiry
      nbf: Math.floor(Date.now() / 1000),
      iat: Math.floor(Date.now() / 1000),
      scope: 'openid voice',
      aud: 'https://api.sinch.com/voice/v1'
    };

    const token = jwt.sign(payload, secret, { algorithm: 'HS256' });
    
    console.log('✅ Sinch token generated successfully');

    res.json({ 
      success: true, 
      data: {
        token,
        appKey: SINCH_CONFIG.APP_KEY,
        appSecret: SINCH_CONFIG.APP_SECRET,
        environment: 'ocra.api.sinch.com'
      }
    });

  } catch (error) {
    console.error('❌ Error generating Sinch token:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to generate token' 
    });
  }
});

module.exports = router; 