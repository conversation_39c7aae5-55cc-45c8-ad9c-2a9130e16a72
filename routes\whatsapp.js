const express = require('express');
const router = express.Router();
const {
  sendWhatsAppMessage,
  getChatMessages,
  markMessagesAsRead,
  sendTemplateMessage,
  getMessageStatus,
  handleWebhook
} = require('../controllers/whatsappController');

// Middleware for authentication (if needed)
// const auth = require('../middleware/auth');

// POST /api/whatsapp/send - Send WhatsApp message to lead
router.post('/send', sendWhatsAppMessage);

// GET /api/whatsapp/messages/:leadId - Get chat messages for a lead
router.get('/messages/:leadId', getChatMessages);

// PUT /api/whatsapp/messages/:leadId/read - Mark messages as read
router.put('/messages/:leadId/read', markMessagesAsRead);

// POST /api/whatsapp/template - Send template message (for opt-in)
router.post('/template', sendTemplateMessage);

// GET /api/whatsapp/status/:messageSid - Get message status
router.get('/status/:messageSid', getMessageStatus);

// POST /api/whatsapp/webhook - Webhook for message status updates and incoming messages
router.post('/webhook', handleWebhook);

module.exports = router; 