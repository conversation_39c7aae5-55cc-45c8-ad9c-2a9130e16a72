// Load environment variables first
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
const http = require('http');
const socketIo = require('socket.io');
const connectDB = require('./config/database');
const errorHandler = require('./middleware/errorHandler');

// Import routes
const authRoutes = require('./routes/auth');
const leadsRoutes = require('./routes/leads');
const callsRoutes = require('./routes/calls');
const sinchRoutes = require('./routes/sinch');
const whatsappRoutes = require('./routes/whatsapp');

// Initialize express
const app = express();

// Create HTTP server
const server = http.createServer(app);

// Initialize Socket.IO with CORS configuration
const io = socketIo(server, {
  cors: {
    origin: true, // Allow all origins in development
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Make io available to routes
app.set('io', io);

// Connect to MongoDB
connectDB();

// Verify Twilio configuration on startup
try {
  const { validateTwilioConfig } = require('./config/twilio');
  if (validateTwilioConfig()) {
    console.log('📱 Twilio WhatsApp integration ready');
  }
} catch (error) {
  console.warn('⚠️ Twilio configuration issue:', error.message);
}

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  }
});
app.use('/api/', limiter);

// CORS - More permissive for development
app.use(cors({
  origin: true, // Allow all origins
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Body parser middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(morgan('dev'));

// Request logging for debugging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Request body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    websockets: 'enabled'
  });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log(`👤 Client connected: ${socket.id}`);

  // Join lead-specific room for targeted messaging
  socket.on('join_lead_room', (leadId) => {
    socket.join(`lead_${leadId}`);
    console.log(`📱 Socket ${socket.id} joined room: lead_${leadId}`);
  });

  // Leave lead room
  socket.on('leave_lead_room', (leadId) => {
    socket.leave(`lead_${leadId}`);
    console.log(`📱 Socket ${socket.id} left room: lead_${leadId}`);
  });

  // Agent typing indicators
  socket.on('agent_typing', (data) => {
    socket.to(`lead_${data.leadId}`).emit('agent_typing', {
      leadId: data.leadId,
      agentName: data.agentName || 'Agent',
      isTyping: data.isTyping
    });
  });

  // Mark messages as read in real-time
  socket.on('mark_messages_read', (data) => {
    socket.to(`lead_${data.leadId}`).emit('messages_read', {
      leadId: data.leadId,
      readAt: new Date()
    });
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    console.log(`👤 Client disconnected: ${socket.id}`);
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/leads', leadsRoutes);
app.use('/api/calls', callsRoutes);
app.use('/api/sinch', sinchRoutes);
app.use('/api/whatsapp', whatsappRoutes);

// 404 handler
app.use('*', (req, res) => {
  console.log(`404 - Route not found: ${req.originalUrl}`);
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`
  });
});

// Error handling middleware
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 5000;

server.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`🔐 Auth endpoints: http://localhost:${PORT}/api/auth`);
  console.log(`📋 Leads endpoints: http://localhost:${PORT}/api/leads`);
  console.log(`📞 Calls endpoints: http://localhost:${PORT}/api/calls`);
  console.log(`📱 WhatsApp endpoints: http://localhost:${PORT}/api/whatsapp`);
  console.log(`⚡ WebSocket server: ws://localhost:${PORT}`);
  console.log(`🌐 CORS: Enabled for all origins`);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  console.log(`❌ Unhandled Rejection: ${err.message}`);
  console.log('Stack:', err.stack);
  // Close server & exit process
  server.close(() => process.exit(1));
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.log(`💥 Uncaught Exception: ${err.message}`);
  console.log('Stack:', err.stack);
  console.log('Shutting down the server due to uncaught exception');
  process.exit(1);
});