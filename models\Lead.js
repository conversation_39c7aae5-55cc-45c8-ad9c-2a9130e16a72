const mongoose = require('mongoose');

const leadSchema = new mongoose.Schema({
  title: { type: String, required: true, trim: true },
  email: { type: String, required: true, trim: true, lowercase: true },
  phone: { type: String, required: true, trim: true },
  status: { type: String, enum: ['new', 'contacted', 'qualified'], default: 'new' },
  priority: { type: String, enum: ['high', 'medium', 'low'], default: 'medium' },
  source: { type: String, required: true, trim: true },
  notes: { type: String, trim: true },
  capturedAt: { type: Date, default: Date.now },
}, {
  timestamps: true
});

leadSchema.virtual('id').get(function() {
  return this._id.toHexString();
});
leadSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('Lead', leadSchema); 