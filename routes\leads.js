const express = require('express');
const router = express.Router();
const Lead = require('../models/Lead');

// GET /api/leads - get all leads
router.get('/', async (req, res) => {
  try {
    const leads = await Lead.find().sort({ capturedAt: -1 });
    res.json({ success: true, data: leads });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// GET /api/leads/:id - get single lead by id
router.get('/:id', async (req, res) => {
  try {
    const lead = await Lead.findById(req.params.id);
    if (!lead) return res.status(404).json({ success: false, message: 'Lead not found' });
    res.json({ success: true, data: lead });
  } catch (error) {
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

module.exports = router; 