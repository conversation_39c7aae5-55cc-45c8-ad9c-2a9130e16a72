const Lead = require('../models/Lead');
const { initiateCall, getCallStatus, endCall } = require('../config/sinch');

// @desc    Initiate call to lead
// @route   POST /api/calls/initiate
// @access  Private
const initiateCallToLead = async (req, res) => {
  try {
    const { leadId } = req.body;
    
    console.log('📞 Initiating call to lead:', leadId);

    // Find the lead
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    console.log('👤 Lead found:', lead.title, 'Phone:', lead.phone);

    // Validate phone number
    if (!lead.phone || lead.phone.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Lead phone number is required'
      });
    }

    // Format phone number (ensure it starts with +)
    let phoneNumber = lead.phone.trim();
    if (!phoneNumber.startsWith('+')) {
      phoneNumber = '+' + phoneNumber;
    }

    console.log('📱 Formatted phone number:', phoneNumber);

    // Initiate call using Sinch
    const callResult = await initiateCall(phoneNumber);
    
    if (!callResult.success) {
      console.error('❌ Call initiation failed:', callResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to initiate call',
        error: callResult.error
      });
    }

    console.log('✅ Call initiated successfully:', callResult.callId);

    // Update lead status to contacted if it was new
    if (lead.status === 'new') {
      lead.status = 'contacted';
      await lead.save();
      console.log('📝 Lead status updated to contacted');
    }

    res.status(200).json({
      success: true,
      message: 'Call initiated successfully',
      data: {
        callId: callResult.callId,
        status: callResult.status,
        leadId: lead._id,
        leadName: lead.title,
        phoneNumber: phoneNumber
      }
    });

  } catch (error) {
    console.error('💥 Call initiation error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during call initiation'
    });
  }
};

// @desc    Get call status
// @route   GET /api/calls/:callId/status
// @access  Private
const getCallStatusById = async (req, res) => {
  try {
    const { callId } = req.params;
    
    console.log('📊 Checking call status for:', callId);

    const statusResult = await getCallStatus(callId);
    
    if (!statusResult.success) {
      console.error('❌ Failed to get call status:', statusResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get call status',
        error: statusResult.error
      });
    }

    console.log('✅ Call status retrieved:', statusResult.status);

    res.status(200).json({
      success: true,
      data: {
        callId,
        status: statusResult.status,
        duration: statusResult.duration,
        result: statusResult.result
      }
    });

  } catch (error) {
    console.error('💥 Get call status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while getting call status'
    });
  }
};

// @desc    End call
// @route   DELETE /api/calls/:callId
// @access  Private
const endCallById = async (req, res) => {
  try {
    const { callId } = req.params;
    
    console.log('🔚 Ending call:', callId);

    const endResult = await endCall(callId);
    
    if (!endResult.success) {
      console.error('❌ Failed to end call:', endResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to end call',
        error: endResult.error
      });
    }

    console.log('✅ Call ended successfully');

    res.status(200).json({
      success: true,
      message: 'Call ended successfully'
    });

  } catch (error) {
    console.error('💥 End call error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while ending call'
    });
  }
};

// @desc    Get call history for a lead
// @route   GET /api/calls/lead/:leadId
// @access  Private
const getCallHistory = async (req, res) => {
  try {
    const { leadId } = req.params;
    
    console.log('📋 Getting call history for lead:', leadId);

    // Find the lead
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    // In a real implementation, you would store call history in a separate collection
    // For now, we'll return basic lead info
    res.status(200).json({
      success: true,
      data: {
        leadId: lead._id,
        leadName: lead.title,
        phoneNumber: lead.phone,
        status: lead.status,
        lastContacted: lead.updatedAt
      }
    });

  } catch (error) {
    console.error('💥 Get call history error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while getting call history'
    });
  }
};

module.exports = {
  initiateCallToLead,
  getCallStatusById,
  endCallById,
  getCallHistory
}; 